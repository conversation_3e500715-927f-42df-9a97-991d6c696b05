using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.API.Controllers.Base;

namespace TravelTourism.API.Controllers.V1;

[ApiController]
[Route("api/v1/[controller]")]
public class FilesController : BaseController
{
    private readonly IFileService _fileService;

    public FilesController(IFileService fileService)
    {
        _fileService = fileService;
    }

    [HttpPost("upload-image")]
    public async Task<IActionResult> UploadImage([FromForm] FileUploadDto fileUploadDto)
    {
        var imageUrl = await _fileService.UploadImageAsync(fileUploadDto);
        return Ok(new { ImageUrl = imageUrl });
    }

    [HttpPost("upload-document")]
    public async Task<IActionResult> UploadDocument([FromForm] FileUploadDto fileUploadDto)
    {
        var documentUrl = await _fileService.UploadDocumentAsync(fileUploadDto);
        return Ok(new { DocumentUrl = documentUrl });
    }
} 