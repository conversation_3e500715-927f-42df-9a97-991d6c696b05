using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using Microsoft.AspNetCore.Authorization;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Auth;
using TravelTourism.Application.Interfaces;


 
namespace TravelTourism.API.Controllers.V1
{ 
    public class BlogsController : BaseController
{
    private readonly IBlogService _blogService;

    public BlogsController(IBlogService blogService)
    {
        _blogService = blogService;
    }

    /// <summary>
    /// Get all blogs with filtering and pagination
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Items per page</param>
    /// <param name="search">Search term</param>
    /// <param name="category">Filter by category</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>Paginated list of blogs</returns>
    [HttpGet]
    public async Task<IActionResult> GetBlogs(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] string? category = null,
        [FromQuery] string sortBy = "CreatedAt",
        [FromQuery] string sortDirection = "desc")
    {
        try
        {
            var pagination = new PaginationParameters { Page = page, PageSize = pageSize };
            var blogs = await _blogService.GetBlogsAsync(pagination, search, null, null, null, sortBy);
            return Ok(CreateSuccessResponse(blogs, "Blogs retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get blog by ID
    /// </summary>
    /// <param name="id">Blog ID</param>
    /// <returns>Blog details</returns>
    [HttpGet("{id:int}")]
    public async Task<IActionResult> GetBlog(int id)
    {
        try
        {
            var blog = await _blogService.GetBlogByIdAsync(id);
            
            if (blog == null)
            {
                return NotFound(CreateErrorResponse("Blog not found", "BLOG_NOT_FOUND"));
            }

            return Ok(CreateSuccessResponse(blog, "Blog retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get all blog categories (public endpoint)
    /// </summary>
    /// <returns>List of blog categories</returns>
    [HttpGet("categories")]
    public async Task<IActionResult> GetBlogCategories()
    {
        try
        {
            // Return all categories for public API (filtering handled at service level)
            var categories = await _blogService.GetBlogCategoriesAsync();
            return Ok(CreateSuccessResponse(categories, "Blog categories retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get featured blogs
    /// </summary>
    /// <param name="count">Number of featured blogs to return</param>
    /// <returns>List of featured blogs</returns>
    [HttpGet("featured")]
    public async Task<IActionResult> GetFeaturedBlogs([FromQuery] int count = 5)
    {
        try
        {
            var pagination = new PaginationParameters { Page = 1, PageSize = count };
            var featuredBlogs = await _blogService.GetFeaturedBlogsAsync(pagination);
            return Ok(CreateSuccessResponse(featuredBlogs, "Featured blogs retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get blogs by category
    /// </summary>
    /// <param name="category">Category name</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Items per page</param>
    /// <returns>Paginated list of blogs in category</returns>
    [HttpGet("category/{categoryId:int}")]
    public async Task<IActionResult> GetBlogsByCategory(
        int categoryId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var pagination = new PaginationParameters { Page = page, PageSize = pageSize };
            var blogs = await _blogService.GetBlogsByCategoryAsync(categoryId, pagination);
            return Ok(CreateSuccessResponse(blogs, $"Blogs in category retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get related blogs
    /// </summary>
    /// <param name="id">Blog ID to find related blogs for</param>
    /// <param name="count">Number of related blogs to return</param>
    /// <returns>List of related blogs</returns>
    [HttpGet("{id:int}/related")]
    public async Task<IActionResult> GetRelatedBlogs(int id, [FromQuery] int count = 5)
    {
        try
        {
            var relatedBlogs = await _blogService.GetRelatedBlogsAsync(id, count);
            return Ok(CreateSuccessResponse(relatedBlogs, "Related blogs retrieved successfully"));
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }
}
}