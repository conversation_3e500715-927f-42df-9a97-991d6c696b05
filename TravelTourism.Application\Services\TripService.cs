using AutoMapper;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Trip;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace TravelTourism.Application.Services
{
    public class TripService : ITripService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public TripService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<ApiResponse<PagedResult<TripDto>>> GetTripsAsync(TripFilterDto filter, PaginationParameters pagination)
        {
            try
            {
                var query = _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Where(t => t.IsActive);

                // Apply filters
                if (filter.CategoryId.HasValue)
                {
                    query = query.Where(t => t.CategoryId == filter.CategoryId.Value);
                }

                if (filter.DestinationId.HasValue)
                {
                    query = query.Where(t => t.DestinationCityId == filter.DestinationId.Value);
                }

                if (filter.MinPrice.HasValue)
                {
                    query = query.Where(t => t.Price >= filter.MinPrice.Value);
                }

                if (filter.MaxPrice.HasValue)
                {
                    query = query.Where(t => t.Price <= filter.MaxPrice.Value);
                }

                if (filter.MinDuration.HasValue)
                {
                    query = query.Where(t => t.Duration >= filter.MinDuration.Value);
                }

                if (filter.MaxDuration.HasValue)
                {
                    query = query.Where(t => t.Duration <= filter.MaxDuration.Value);
                }

                if (filter.Difficulty.HasValue)
                {
                    query = query.Where(t => t.Difficulty == filter.Difficulty.Value);
                }

                if (filter.AvailableFrom.HasValue)
                {
                    query = query.Where(t => t.AvailableFrom >= filter.AvailableFrom.Value);
                }

                if (filter.AvailableTo.HasValue)
                {
                    query = query.Where(t => t.AvailableTo <= filter.AvailableTo.Value);
                }

                // Apply sorting
                query = filter.SortBy?.ToLower() switch
                {
                    "price" => filter.SortOrder == "desc" ? query.OrderByDescending(t => t.Price) : query.OrderBy(t => t.Price),
                    "duration" => filter.SortOrder == "desc" ? query.OrderByDescending(t => t.Duration) : query.OrderBy(t => t.Duration),
                    _ => query.OrderBy(t => t.Name)
                };

                var totalCount = await query.CountAsync();
                var trips = await query
                    .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                    .Take(pagination.PageSize)
                    .ToListAsync();

                var tripDtos = _mapper.Map<List<TripDto>>(trips);

                var pagedResult = new PagedResult<TripDto>(tripDtos, totalCount, pagination.PageNumber, pagination.PageSize);
                return ApiResponse<PagedResult<TripDto>>.SuccessResponse(pagedResult);
            }
            catch (Exception ex)
            {
                return ApiResponse<PagedResult<TripDto>>.ErrorResponse($"Error retrieving trips: {ex.Message}");
            }
        }

        public async Task<ApiResponse<TripDetailDto>> GetTripByIdAsync(int id)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Include(t => t.Itineraries.OrderBy(i => i.Day))
                    .Include(t => t.Images)
                    .FirstOrDefaultAsync(t => t.Id == id && t.IsActive);

                if (trip == null)
                {
                    return ApiResponse<TripDetailDto>.ErrorResponse("Trip not found", new List<string> { "Not found" });
                }

                var tripDetailDto = _mapper.Map<TripDetailDto>(trip);
                return ApiResponse<TripDetailDto>.SuccessResponse(tripDetailDto);
            }
            catch (Exception ex)
            {
                return ApiResponse<TripDetailDto>.ErrorResponse($"Error retrieving trip: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<TripDto>>> GetFeaturedTripsAsync(int count = 10)
        {
            try
            {
                var trips = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Where(t => t.IsActive && t.IsFeatured)
                    .OrderByDescending(t => t.CreatedAt)
                    .Take(count)
                    .ToListAsync();

                var tripDtos = _mapper.Map<List<TripDto>>(trips);
                return ApiResponse<List<TripDto>>.SuccessResponse(tripDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<TripDto>>.ErrorResponse($"Error retrieving featured trips: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<TripCategoryDto>>> GetCategoriesAsync()
        {
            try
            {
                var categories = await _unitOfWork.Trips.GetCategoriesAsync();
                var categoryDtos = _mapper.Map<List<TripCategoryDto>>(categories);
                return ApiResponse<List<TripCategoryDto>>.SuccessResponse(categoryDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<TripCategoryDto>>.ErrorResponse($"Error retrieving categories: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<CityDto>>> GetDestinationsAsync()
        {
            try
            {
                var cities = await _unitOfWork.Cities.GetAllAsync();
                var cityDtos = _mapper.Map<List<CityDto>>(cities);
                return ApiResponse<List<CityDto>>.SuccessResponse(cityDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<CityDto>>.ErrorResponse($"Error retrieving destinations: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> IsTripAvailableAsync(int tripId, DateTime travelDate, int numberOfPeople)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetByIdAsync(tripId);
                if (trip == null || !trip.IsActive)
                {
                    return ApiResponse<bool>.ErrorResponse("Trip not found or not active");
                }

                // Check if the travel date is within the available period
                if (travelDate < trip.AvailableFrom || travelDate > trip.AvailableTo)
                {
                    return ApiResponse<bool>.SuccessResponse(false);
                }

                // Check if there are enough available spots
                var existingBookings = await _unitOfWork.Bookings.GetQueryable()
                    .Where(b => b.TripId == tripId && 
                               b.TravelDate.Date == travelDate.Date && 
                               b.Status != Core.Enums.BookingStatus.Cancelled)
                    .SumAsync(b => b.TotalPeople);

                var availableSpots = trip.MaxGroupSize - existingBookings;
                var isAvailable = availableSpots >= numberOfPeople;

                return ApiResponse<bool>.SuccessResponse(isAvailable);
            }
            catch (Exception ex)
            {
                return ApiResponse<bool>.ErrorResponse($"Error checking trip availability: {ex.Message}");
            }
        }

        public async Task<ApiResponse<TripDetailDto>> CreateTripAsync(CreateTripRequest createTripRequest)
        {
            try
            {
                var trip = _mapper.Map<Trip>(createTripDto);
                trip.CreatedAt = DateTime.UtcNow;
                trip.IsActive = true;

                await _unitOfWork.Trips.AddAsync(trip);
                await _unitOfWork.SaveChangesAsync();

                var createdTrip = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Include(t => t.Itineraries)
                    .Include(t => t.Images)
                    .FirstOrDefaultAsync(t => t.Id == trip.Id);

                var tripDetailDto = _mapper.Map<TripDetailDto>(createdTrip);
                return ApiResponse<TripDetailDto>.SuccessResponse(tripDetailDto);
            }
            catch (Exception ex)
            {
                return ApiResponse<TripDetailDto>.ErrorResponse($"Error creating trip: {ex.Message}");
            }
        }

        public async Task<ApiResponse<TripDetailDto>> UpdateTripAsync(UpdateTripRequest updateTripRequest)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Itineraries)
                    .Include(t => t.Images)
                    .FirstOrDefaultAsync(t => t.Id == updateTripDto.Id);

                if (trip == null)
                {
                    return ApiResponse<TripDetailDto>.ErrorResponse("Trip not found", new List<string> { "Not found" });
                }

                _mapper.Map(updateTripDto, trip);
                trip.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.Trips.UpdateAsync(trip);
                await _unitOfWork.SaveChangesAsync();

                var updatedTrip = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Include(t => t.Itineraries)
                    .Include(t => t.Images)
                    .FirstOrDefaultAsync(t => t.Id == trip.Id);

                var tripDetailDto = _mapper.Map<TripDetailDto>(updatedTrip);
                return ApiResponse<TripDetailDto>.SuccessResponse(tripDetailDto);
            }
            catch (Exception ex)
            {
                return ApiResponse<TripDetailDto>.ErrorResponse($"Error updating trip: {ex.Message}");
            }
        }

        public async Task<ApiResponse> DeleteTripAsync(int id)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetByIdAsync(id);
                if (trip == null)
                {
                    return ApiResponse.ErrorResponse("Trip not found", new List<string> { "Not found" });
                }

                // Check if there are any active bookings for this trip
                var hasActiveBookings = await _unitOfWork.Bookings.GetQueryable()
                    .AnyAsync(b => b.TripId == id && b.Status != Core.Enums.BookingStatus.Cancelled);

                if (hasActiveBookings)
                {
                    return ApiResponse.ErrorResponse("Cannot delete trip with active bookings", new List<string> { "Bad request" });
                }

                // Soft delete by setting IsActive to false
                trip.IsActive = false;
                trip.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.Trips.UpdateAsync(trip);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse("Trip deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse($"Error deleting trip: {ex.Message}");
            }
        }

        public async Task<ApiResponse> ToggleFeaturedAsync(int id)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetByIdAsync(id);
                if (trip == null)
                {
                    return ApiResponse.ErrorResponse("Trip not found", new List<string> { "Not found" });
                }

                trip.IsFeatured = !trip.IsFeatured;
                trip.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.Trips.UpdateAsync(trip);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse($"Trip featured status updated to {trip.IsFeatured}");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse($"Error updating featured status: {ex.Message}");
            }
        }

        public async Task<ApiResponse> ToggleActiveAsync(int id)
        {
            try
            {
                var trip = await _unitOfWork.Trips.GetByIdAsync(id);
                if (trip == null)
                {
                    return ApiResponse.ErrorResponse("Trip not found", new List<string> { "Not found" });
                }

                trip.IsActive = !trip.IsActive;
                trip.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.Trips.UpdateAsync(trip);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse($"Trip active status updated to {trip.IsActive}");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse($"Error updating active status: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<TripDto>>> SearchTripsAsync(string searchTerm, int? categoryId = null, int? destinationId = null)
        {
            try
            {
                var query = _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Where(t => t.IsActive);

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(t => t.Name.Contains(searchTerm) || 
                                            t.Description.Contains(searchTerm) ||
                                            t.DestinationCity.Name.Contains(searchTerm) ||
                                            t.Category.Name.Contains(searchTerm));
                }

                if (categoryId.HasValue)
                {
                    query = query.Where(t => t.CategoryId == categoryId.Value);
                }

                if (destinationId.HasValue)
                {
                    query = query.Where(t => t.DestinationCityId == destinationId.Value);
                }

                var trips = await query
                    .OrderBy(t => t.Price)
                    .ToListAsync();

                var tripDtos = _mapper.Map<List<TripDto>>(trips);
                return ApiResponse<List<TripDto>>.SuccessResponse(tripDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<TripDto>>.ErrorResponse($"Error searching trips: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<TripDto>>> GetTripsByCategoryAsync(int categoryId)
        {
            try
            {
                var trips = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Where(t => t.CategoryId == categoryId && t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                var tripDtos = _mapper.Map<List<TripDto>>(trips);
                return ApiResponse<List<TripDto>>.SuccessResponse(tripDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<TripDto>>.ErrorResponse($"Error retrieving trips by category: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<TripDto>>> GetTripsByDestinationAsync(int cityId)
        {
            try
            {
                var trips = await _unitOfWork.Trips.GetQueryable()
                    .Include(t => t.Category)
                    .Include(t => t.DestinationCity)
                    .Where(t => t.DestinationCityId == cityId && t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                var tripDtos = _mapper.Map<List<TripDto>>(trips);
                return ApiResponse<List<TripDto>>.SuccessResponse(tripDtos);
            }
            catch (Exception ex)
            {
                return ApiResponse<List<TripDto>>.ErrorResponse($"Error retrieving trips by destination: {ex.Message}");
            }
        }
    }
}
