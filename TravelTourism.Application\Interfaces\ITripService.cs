using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Trip;

namespace TravelTourism.Application.Interfaces
{
    public interface ITripService
    {
        // Public endpoints
        Task<ApiResponse<PagedResult<TripDto>>> GetTripsAsync(TripFilterDto filter, PaginationParameters pagination);
        Task<ApiResponse<TripDetailDto>> GetTripByIdAsync(int id);
        Task<ApiResponse<List<TripDto>>> GetFeaturedTripsAsync(int count = 10);
        Task<ApiResponse<List<TripCategoryDto>>> GetCategoriesAsync();
        Task<ApiResponse<List<CityDto>>> GetDestinationsAsync();
        Task<ApiResponse<bool>> IsTripAvailableAsync(int tripId, DateTime travelDate, int numberOfPeople);

        // Admin endpoints
        Task<ApiResponse<TripDetailDto>> CreateTripAsync(CreateTripRequest createTripRequest);
        Task<ApiResponse<TripDetailDto>> UpdateTripAsync(UpdateTripRequest updateTripRequest);
        Task<ApiResponse> DeleteTripAsync(int id);
        Task<ApiResponse> ToggleFeaturedAsync(int id);
        Task<ApiResponse> ToggleActiveAsync(int id);

        // Search and filtering
        Task<ApiResponse<List<TripDto>>> SearchTripsAsync(string searchTerm, int? categoryId = null, int? destinationId = null);
        Task<ApiResponse<List<TripDto>>> GetTripsByCategoryAsync(int categoryId);
        Task<ApiResponse<List<TripDto>>> GetTripsByDestinationAsync(int cityId);
    }
}
