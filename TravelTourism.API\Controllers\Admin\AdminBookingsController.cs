using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;

namespace TravelTourism.Controllers.Admin
{
    [ApiController]
    [Route("api/v1/admin/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class AdminBookingsController : BaseController
    {
        private readonly IBookingService _bookingService;
        private readonly IAdminService _adminService;

        public AdminBookingsController(IBookingService bookingService, IAdminService adminService)
        {
            _bookingService = bookingService;
            _adminService = adminService;
        }

        /// <summary>
        /// Get all bookings with pagination and filtering
        /// </summary>
        /// <param name="request">Bookings filter and pagination request</param>
        /// <returns>Paginated list of bookings</returns>
        [HttpGet]
        public async Task<IActionResult> GetBookings([FromQuery] AdminBookingFilterRequest request)
        {
            try
            {
                var bookings = await _adminService.GetBookingsAsync(request);
                return Ok(CreateSuccessResponse(bookings, "Bookings retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking by ID
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Booking details</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetBooking(int id)
        {
            try
            {
                var booking = await _bookingService.GetBookingByIdAsync(id);

                if (booking == null)
                {
                    return NotFound(CreateErrorResponse("Booking not found", "BOOKING_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(booking, "Booking retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update booking details
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Update booking request</param>
        /// <returns>Updated booking details</returns>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdateBooking(int id, [FromBody] TravelTourism.Application.DTOs.Admin.UpdateBookingRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _bookingService.UpdateBookingAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Cancel booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Cancel booking request</param>
        /// <returns>Cancellation result</returns>
        [HttpPost("{id:int}/cancel")]
        public async Task<IActionResult> CancelBooking(int id, [FromBody] CancelBookingRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _bookingService.CancelBookingAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking cancelled successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteBooking(int id)
        {
            try
            {
                var result = await _bookingService.DeleteBookingAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(null, "Booking deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Confirm booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Confirmation result</returns>
        [HttpPost("{id:int}/confirm")]
        public async Task<IActionResult> ConfirmBooking(int id)
        {
            try
            {
                var result = await _adminService.ConfirmBookingAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking confirmed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Process booking refund
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Refund request</param>
        /// <returns>Refund result</returns>
        [HttpPost("{id:int}/refund")]
        public async Task<IActionResult> ProcessRefund(int id, [FromBody] ProcessRefundRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.ProcessRefundAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Refund processed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update booking status
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Status update request</param>
        /// <returns>Update result</returns>
        [HttpPut("{id:int}/status")]
        public async Task<IActionResult> UpdateBookingStatus(int id, [FromBody] UpdateBookingStatusRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateBookingStatusAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking status updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking statistics
        /// </summary>
        /// <returns>Booking statistics</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetBookingStatistics()
        {
            try
            {
                var statistics = await _adminService.GetBookingStatisticsAsync();
                return Ok(CreateSuccessResponse(statistics, "Booking statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking revenue statistics
        /// </summary>
        /// <param name="request">Revenue statistics request</param>
        /// <returns>Revenue statistics</returns>
        [HttpGet("revenue-statistics")]
        public async Task<IActionResult> GetRevenueStatistics([FromQuery] RevenueStatisticsRequest request)
        {
            try
            {
                var statistics = await _adminService.GetRevenueStatisticsAsync(request);
                return Ok(CreateSuccessResponse(statistics, "Revenue statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking analytics
        /// </summary>
        /// <param name="request">Analytics request</param>
        /// <returns>Booking analytics</returns>
        [HttpGet("analytics")]
        public async Task<IActionResult> GetBookingAnalytics([FromQuery] BookingAnalyticsRequest request)
        {
            try
            {
                var analytics = await _adminService.GetBookingAnalyticsAsync(request);
                return Ok(CreateSuccessResponse(analytics, "Booking analytics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking reports
        /// </summary>
        /// <param name="request">Report request</param>
        /// <returns>Booking reports</returns>
        [HttpGet("reports")]
        public async Task<IActionResult> GetBookingReports([FromQuery] BookingReportRequest request)
        {
            try
            {
                var reports = await _adminService.GetBookingReportsAsync(request);
                return Ok(CreateSuccessResponse(reports, "Booking reports retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export bookings to CSV
        /// </summary>
        /// <param name="request">Export request</param>
        /// <returns>CSV file</returns>
        [HttpGet("export")]
        public async Task<IActionResult> ExportBookings([FromQuery] ExportBookingsRequest request)
        {
            try
            {
                var result = await _adminService.ExportBookingsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return File(result.Data, "text/csv", "bookings.csv");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export booking invoices to PDF
        /// </summary>
        /// <param name="request">Export request</param>
        /// <returns>PDF file</returns>
        [HttpGet("export-invoices")]
        public async Task<IActionResult> ExportBookingInvoices([FromQuery] ExportInvoicesRequest request)
        {
            try
            {
                var result = await _adminService.ExportBookingInvoicesAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return File(result.Data, "application/pdf", "booking-invoices.pdf");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Send booking reminder
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Reminder request</param>
        /// <returns>Reminder result</returns>
        [HttpPost("{id:int}/reminder")]
        public async Task<IActionResult> SendBookingReminder(int id, [FromBody] SendReminderRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.SendBookingReminderAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking reminder sent successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking payment details
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Payment details</returns>
        [HttpGet("{id:int}/payment")]
        public async Task<IActionResult> GetBookingPayment(int id)
        {
            try
            {
                var payment = await _adminService.GetBookingPaymentAsync(id);

                if (payment == null)
                {
                    return NotFound(CreateErrorResponse("Payment not found", "PAYMENT_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(payment, "Payment details retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking customer details
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Customer details</returns>
        [HttpGet("{id:int}/customer")]
        public async Task<IActionResult> GetBookingCustomer(int id)
        {
            try
            {
                var customer = await _adminService.GetBookingCustomerAsync(id);

                if (customer == null)
                {
                    return NotFound(CreateErrorResponse("Customer not found", "CUSTOMER_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(customer, "Customer details retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking trip details
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Trip details</returns>
        [HttpGet("{id:int}/trip")]
        public async Task<IActionResult> GetBookingTrip(int id)
        {
            try
            {
                var trip = await _adminService.GetBookingTripAsync(id);

                if (trip == null)
                {
                    return NotFound(CreateErrorResponse("Trip not found", "TRIP_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(trip, "Trip details retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Add booking note
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Note request</param>
        /// <returns>Note result</returns>
        [HttpPost("{id:int}/notes")]
        public async Task<IActionResult> AddBookingNote(int id, [FromBody] AddBookingNoteRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.AddBookingNoteAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking note added successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking notes
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Booking notes</returns>
        [HttpGet("{id:int}/notes")]
        public async Task<IActionResult> GetBookingNotes(int id)
        {
            try
            {
                var notes = await _adminService.GetBookingNotesAsync(id);
                return Ok(CreateSuccessResponse(notes, "Booking notes retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update booking note
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="noteId">Note ID</param>
        /// <param name="request">Update note request</param>
        /// <returns>Update result</returns>
        [HttpPut("{id:int}/notes/{noteId:int}")]
        public async Task<IActionResult> UpdateBookingNote(int id, int noteId, [FromBody] UpdateBookingNoteRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateBookingNoteAsync(id, noteId, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Booking note updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete booking note
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="noteId">Note ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}/notes/{noteId:int}")]
        public async Task<IActionResult> DeleteBookingNote(int id, int noteId)
        {
            try
            {
                var result = await _adminService.DeleteBookingNoteAsync(id, noteId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(null, "Booking note deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bulk update bookings
        /// </summary>
        /// <param name="request">Bulk update request</param>
        /// <returns>Update result</returns>
        [HttpPost("bulk-update")]
        public async Task<IActionResult> BulkUpdateBookings([FromBody] BulkUpdateBookingsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.BulkUpdateBookingsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Bookings updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking activity log
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Activity log</returns>
        [HttpGet("{id:int}/activity")]
        public async Task<IActionResult> GetBookingActivityLog(int id)
        {
            try
            {
                var activities = await _adminService.GetBookingActivityLogAsync(id);
                return Ok(CreateSuccessResponse(activities, "Booking activity log retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}


