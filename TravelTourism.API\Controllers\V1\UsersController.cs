using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Core.Enums;
using TravelTourism.Application.Interfaces;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.DTOs.Auth;
using TravelTourism.Application.DTOs.User;


namespace TravelTourism.API.Controllers.V1
{
    [Authorize]
    public class UsersController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IBookingService _bookingService;

        public UsersController(IUserService userService, IBookingService bookingService)
        {
            _userService = userService;
            _bookingService = bookingService;
        }

        /// <summary>
        /// Get current user's profile
        /// </summary>
        /// <returns>User profile information</returns>
        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                var user = await _userService.GetUserByIdAsync(userId);

                if (user == null)
                {
                    return NotFound(CreateErrorResponse("User not found", "USER_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(user, "Profile retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update current user's profile
        /// </summary>
        /// <param name="updateUserDto">User update information</param>
        /// <returns>Updated user profile</returns>
        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateUserDto updateUserDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var userId = GetCurrentUserId();
                var updatedUser = await _userService.UpdateUserAsync(userId, updateUserDto);

                if (updatedUser == null)
                {
                    return NotFound(CreateErrorResponse("User not found", "USER_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(updatedUser, "Profile updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get current user's bookings
        /// </summary>
        /// <param name="status">Filter by booking status</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Items per page</param>
        /// <returns>List of user bookings</returns>
        [HttpGet("bookings")]
        public async Task<IActionResult> GetUserBookings(
            [FromQuery] BookingStatus? status = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var userId = GetCurrentUserId();
                var bookings = await _bookingService.GetUserBookingsAsync(userId, status, page, pageSize);

                return Ok(CreateSuccessResponse(bookings, "Bookings retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete current user's account
        /// </summary>
        /// <returns>Success response</returns>
        [HttpDelete("account")]
        public async Task<IActionResult> DeleteAccount()
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _userService.DeleteUserAsync(userId);

                if (!success)
                {
                    return NotFound(CreateErrorResponse("User not found", "USER_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(null, "Account deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}

