using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.API.Models.Requests;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Trip;
using TravelTourism.Application.Interfaces;

namespace TravelTourismAPI.Controllers.Admin
{
    [ApiController]
    [Route("api/v1/admin/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class AdminTripsController : BaseController
    {
        private readonly ITripService _tripService;
        private readonly IAdminService _adminService;

        public AdminTripsController(ITripService tripService, IAdminService adminService)
        {
            _tripService = tripService;
            _adminService = adminService;
        }

        /// <summary>
        /// Get all trips with pagination and filtering
        /// </summary>
        /// <param name="request">Trips filter and pagination request</param>
        /// <returns>Paginated list of trips</returns>
        [HttpGet]
        public async Task<IActionResult> GetTrips([FromQuery] AdminTripFilterRequest request)
        {
            try
            {
                var trips = await _adminService.GetTripsAsync(request);
                return Ok(CreateSuccessResponse(trips, "Trips retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get trip by ID
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Trip details</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetTrip(int id)
        {
            try
            {
                var trip = await _tripService.GetTripByIdAsync(id);

                if (trip == null)
                {
                    return NotFound(CreateErrorResponse("Trip not found", "TRIP_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(trip, "Trip retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create a new trip
        /// </summary>
        /// <param name="request">Trip creation request</param>
        /// <returns>Created trip details</returns>
        [HttpPost]
        public async Task<IActionResult> CreateTrip([FromBody] CreateTripRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _tripService.CreateTripAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return CreatedAtAction(
                    nameof(GetTrip),
                    new { id = result.Data.Id },
                    CreateSuccessResponse(result.Data, "Trip created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update trip details
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <param name="request">Update trip request</param>
        /// <returns>Updated trip details</returns>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdateTrip(int id, [FromBody] UpdateTripRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _tripService.UpdateTripAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteTrip(int id)
        {
            try
            {
                var result = await _tripService.DeleteTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(null, "Trip deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Publish trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Publication result</returns>
        [HttpPost("{id:int}/publish")]
        public async Task<IActionResult> PublishTrip(int id)
        {
            try
            {
                var result = await _adminService.PublishTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip published successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Unpublish trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Unpublication result</returns>
        [HttpPost("{id:int}/unpublish")]
        public async Task<IActionResult> UnpublishTrip(int id)
        {
            try
            {
                var result = await _adminService.UnpublishTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip unpublished successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Feature trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Feature result</returns>
        [HttpPost("{id:int}/feature")]
        public async Task<IActionResult> FeatureTrip(int id)
        {
            try
            {
                var result = await _adminService.FeatureTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip featured successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Unfeature trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Unfeature result</returns>
        [HttpPost("{id:int}/unfeature")]
        public async Task<IActionResult> UnfeatureTrip(int id)
        {
            try
            {
                var result = await _adminService.UnfeatureTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip unfeatured successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get trip bookings
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <param name="request">Pagination request</param>
        /// <returns>Trip bookings</returns>
        [HttpGet("{id:int}/bookings")]
        public async Task<IActionResult> GetTripBookings(int id, [FromQuery] PaginationRequest request)
        {
            try
            {
                var pagination = new PaginationParameters
                {
                    PageNumber = request.Page,
                    PageSize = request.PageSize
                };
                var bookings = await _adminService.GetTripBookingsAsync(id, pagination);
                return Ok(CreateSuccessResponse(bookings, "Trip bookings retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get trip statistics
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Trip statistics</returns>
        [HttpGet("{id:int}/statistics")]
        public async Task<IActionResult> GetTripStatistics(int id)
        {
            try
            {
                var statistics = await _adminService.GetTripStatisticsAsync(id);
                return Ok(CreateSuccessResponse(statistics, "Trip statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Upload trip images
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <param name="files">Image files</param>
        /// <returns>Upload result</returns>
        [HttpPost("{id:int}/images")]
        public async Task<IActionResult> UploadTripImages(int id, IList<IFormFile> files)
        {
            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest(CreateErrorResponse("No files provided", "NO_FILES"));
                }

                var result = await _adminService.UploadTripImagesAsync(id, files.ToList());

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Images uploaded successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete trip image
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <param name="imageId">Image ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}/images/{imageId:int}")]
        public async Task<IActionResult> DeleteTripImage(int id, int imageId)
        {
            try
            {
                var result = await _adminService.DeleteTripImageAsync(id, imageId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(null, "Image deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Duplicate trip
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Duplicated trip</returns>
        [HttpPost("{id:int}/duplicate")]
        public async Task<IActionResult> DuplicateTrip(int id)
        {
            try
            {
                var result = await _adminService.DuplicateTripAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trip duplicated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get trip reviews
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <param name="request">Pagination request</param>
        /// <returns>Trip reviews</returns>
        [HttpGet("{id:int}/reviews")]
        public async Task<IActionResult> GetTripReviews(int id, [FromQuery] PaginationRequest request)
        {
            try
            {
                var pagination = new PaginationParameters
                {
                    PageNumber = request.Page,
                    PageSize = request.PageSize
                };
                var reviews = await _adminService.GetTripReviewsAsync(id, pagination);
                return Ok(CreateSuccessResponse(reviews, "Trip reviews retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bulk update trip status
        /// </summary>
        /// <param name="request">Bulk update request</param>
        /// <returns>Update result</returns>
        [HttpPost("bulk-update")]
        public async Task<IActionResult> BulkUpdateTrips([FromBody] BulkUpdateTripsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.BulkUpdateTripsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Trips updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export trips to CSV
        /// </summary>
        /// <param name="request">Export request</param>
        /// <returns>CSV file</returns>
        [HttpGet("export")]
        public async Task<IActionResult> ExportTrips([FromQuery] ExportTripsRequest request)
        {
            try
            {
                var result = await _adminService.ExportTripsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return File(result.Data, "text/csv", "trips.csv");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}


